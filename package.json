{"name": "bingot<PERSON>", "version": "4.0.2", "channel_config": "cuiya", "API": "https://qa.binguoketang.com/", "BASE_API": "https://api.qa.bingotalk.cn", "API_BACKUP": "https://s.qa.cuiya.cn/", "build": {"extraMetadata": {"main": "build/index.js"}, "productName": "缤果课堂", "appId": "com.bingotalk.app.qa", "files": ["build/**/*"], "mac": {"identity": "Beijing Yinghua High Technology Co., Ltd. (A63RAW4Q6W)", "target": ["dmg", "zip"], "type": "distribution", "icon": "icon.icns", "entitlements": "entitlements.mac.plist", "entitlementsInherit": "entitlements.mac.plist", "artifactName": "${productName}_setup_${version}.${ext}", "hardenedRuntime": true, "gatekeeperAssess": false, "publish": [{"provider": "generic", "url": "https://bj.static.bingotalk.cn/static/qa/macStu"}], "extendInfo": {"NSMicrophoneUsageDescription": "请允许本程序访问您的麦克风", "NSCameraUsageDescription": "请允许本程序访问您的摄像头", "NSLocationUsageDescription": "请允许本程序访问您的位置信息，以便为您提供更好的服务"}}, "dmg": {"sign": false, "artifactName": "${productName}_setup_${version}.${ext}"}, "win": {"icon": "icon.ico", "artifactName": "${productName}_setup_${version}.${ext}", "publish": [{"provider": "generic", "url": "https://bj.static.bingotalk.cn/static/qa/windows"}], "target": [{"target": "nsis", "arch": ["ia32"]}]}, "nsis": {"allowToChangeInstallationDirectory": true, "oneClick": false, "artifactName": "${productName}_setup_${version}.${ext}", "installerHeaderIcon": "icon.ico", "installerIcon": "icon.ico", "uninstallerIcon": "icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "缤果课堂"}, "linux": {"icon": "icons/favicon.png", "target": ["deb", "rpm", "snap", "AppImage"], "category": "Development"}, "directories": {"buildResources": "assets", "output": "release"}, "afterSign": "notarize.js"}, "scripts": {"electron": "cross-env NODE_ENV=development electron ./app/", "dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:qa": "vue-cli-service build --mode qa", "build:stage": "vue-cli-service build --mode staging", "electron:copy:electron": "cpx \"./app/**/*.{js,html}\" ./build", "electron:build": "npm run electron:copy:electron", "pack:mac": "cross-env ELECTRON_BUILDER_BINARIES_MIRROR=https://npmmirror.com/mirrors/electron-builder-binaries/ npm run channel && npm run electron:build && electron-builder --mac --x64", "pack:win": "cross-env ELECTRON_BUILDER_BINARIES_MIRROR=https://npmmirror.com/mirrors/electron-builder-binaries/ npm run channel && npm run electron:build && electron-builder --win", "pack:all": "npm run channel && npm run electron:build && electron-builder --win && electron-builder --mac --x64", "pack:mac:qa": "npm run channel 'cuiya' qa && npm run electron:build && electron-builder --mac --x64", "pack:win:qa": "npm run channel 'cuiya' qa && npm run electron:build && electron-builder --win", "pack:all:qa": "npm run channel 'cuiya' qa && npm run electron:build && electron-builder --win && electron-builder --mac --x64", "pack:mac:wx": "npm run channel 'wenxuan' && npm run electron:build && electron-builder --mac --x64", "pack:win:wx": "npm run channel 'wenxuan' && npm run electron:build && electron-builder --win", "pack:all:wx": "npm run channel 'wenxuan' && npm run electron:build && electron-builder --win && electron-builder --mac --x64", "pack:mac:db": "npm run channel 'digitalbook' && npm run electron:build && electron-builder --mac --x64", "pack:win:db": "npm run channel 'digitalbook' && npm run electron:build && electron-builder --win", "pack:all:db": "npm run channel 'digitalbook' && npm run electron:build && electron-builder --win && electron-builder --mac --x64", "pack:mac:wx:qa": "npm run channel 'wenxuan' qa && npm run electron:build && electron-builder --mac --x64", "pack:win:wx:qa": "npm run channel 'wenxuan' qa && npm run electron:build && electron-builder --win", "pack:all:wx:qa": "npm run channel 'wenxuan' qa && npm run electron:build && electron-builder --win && electron-builder --mac --x64", "pack:mac:db:qa": "npm run channel 'digitalbook' qa && npm run electron:build && electron-builder --mac --x64", "pack:win:db:qa": "npm run channel 'digitalbook' qa && npm run electron:build && electron-builder --win", "pack:all:db:qa": "npm run channel 'digitalbook' qa && npm run electron:build && electron-builder --win && electron-builder --mac --x64", "pack:mac:aigc": "npm run channel 'aigc' && npm run electron:build && electron-builder --mac --x64", "pack:win:aigc": "npm run channel 'aigc' && npm run electron:build && electron-builder --win", "pack:all:aigc": "npm run channel 'aigc' && npm run electron:build && electron-builder --win && electron-builder --mac --x64", "pack:mac:aigc:qa": "npm run channel 'aigc' qa && npm run electron:build && PYTHON=/usr/local/bin/python electron-builder --mac --x64", "pack:win:aigc:qa": "npm run channel 'aigc' qa && npm run electron:build && electron-builder --win", "pack:all:aigc:qa": "npm run channel 'aigc' qa && npm run electron:build && electron-builder --win && electron-builder --mac --x64", "lint": "vue-cli-service lint", "report": "npm run build:qa --report", "channel": "node ./scripts/update-channel.js"}, "dependencies": {"@antv/g2plot": "^2.4.31", "@netless/video-js-plugin": "^0.3.8", "@npkg/tinymce-plugins": "^0.0.7", "@tinymce/tinymce-vue": "^3.2.8", "@vant/area-data": "^1.4.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "abort-controller": "^3.0.0", "agora-rtc-sdk-ng": "^4.8.2", "axios": "^0.23.0", "callapp-lib": "^3.5.3", "core-js": "^3.6.5", "dayjs": "^1.10.6", "docx": "^9.5.1", "echarts": "^5.5.0", "electron-differential-updater": "4.3.2", "electron-store": "^8.0.1", "electron-updater": "^4.6.1", "element-china-area-data": "^6.1.0", "element-ui": "^2.15.6", "file-saver": "^2.0.5", "find5": "^1.0.22", "gsap": "^3.12.7", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "jquery": "^3.7.1", "js-cookie": "2.2.1", "jsencrypt": "3.0.0-rc.1", "jwchat": "^1.1.1", "jweixin-module": "^1.6.0", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "marked": "^16.1.0", "marked-highlight": "^2.2.2", "mathjax": "3.2.2", "mathml2latex": "^1.1.3", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "mousetrap": "^1.6.5", "node-fetch": "^2.6.7", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pdfjs-dist": "2.6.347", "pptx-parser": "^1.1.7-beta.9", "prismjs": "^1.29.0", "qrcodejs2": "0.0.2", "screenfull": "^5.2.0", "snabbdom": "^3.3.1", "stream-to-blob": "^2.0.1", "svgaplayerweb": "^2.3.2", "swiper": "^5.3.7", "tinymce": "^5.10.3", "tinymce-paragraphspacing": "^1.0.1", "uuid": "^9.0.1", "v-viewer": "^1.7.4", "vant": "^2.12.48", "vconsole": "^3.15.0", "video.js": "^7.11.0", "videojs-contrib-hls": "^5.15.0", "vue": "^2.6.11", "vue-awesome-swiper": "^4.1.0", "vue-clipboard2": "^0.3.3", "vue-i18n": "^8.26.7", "vue-lottie": "^0.2.1", "vue-qrcode-reader": "^3.1.0", "vue-router": "^3.2.0", "vue-script2": "^2.1.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "web-highlighter": "^0.7.4", "white-web-sdk": "^2.15.12", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.11.0", "@electron/notarize": "^2.1.0", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "4.4.4", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^8.2.2", "babel-plugin-dynamic-import-node": "^2.3.3", "cpx": "^1.5.0", "cross-env": "^7.0.3", "electron": "^16.0.6", "electron-builder": "^23.6.0", "electron-log": "^4.4.6", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "html-webpack-plugin": "^3.2.0", "postcss-px-to-viewport": "^1.1.1", "raw-loader": "^4.0.2", "sass": "~1.39.0", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "^2.1.5", "svg-sprite-loader": "4.1.3", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.5.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "windowTitleConfig": {"showInTitle": false, "showInAbout": true}, "menuDisplayName": "缤果课堂"}