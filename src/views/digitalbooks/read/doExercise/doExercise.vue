<template>
<!--  <div class="do-exercise-main" v-if='show' v-loading="loading" element-loading-text="AI分析中请稍等">-->
  <div class="do-exercise-main" v-if='show'>
    <div class="flex items-center close-view" @click="close" style="cursor: pointer">
      <i class="el-icon-arrow-left"></i>
      <span>返回</span>
    </div>
    <div class="main-view">
      <div class="header-view">
        <div class="header-title">
          {{ testTitle }}
        </div>
        <div class="header-info">
          <span class='mr5'>分数情况：</span>
          <span class='mr5' v-if="choiceList.length !== 0">选择题 共{{ choiceScore.num }}道 <span v-if="choiceScore.score" class="score_num">{{ choiceScore.score }}分</span></span>
          <span class='mr5' v-if="fillList.length !== 0">填空题 共{{ fillScore.num }}道 <span v-if="fillScore.score" class="score_num">{{ fillScore.score }}分</span></span>
          <span class='mr5' v-if="simpleList.length !== 0">判断题 共{{ simpleScore.num }}道 <span v-if="simpleScore.score" class="score_num">{{ simpleScore.score }}分</span></span>
          <span class='mr5' v-if="essayList.length !== 0">简答题 共{{ essayScore.num }}道 <span v-if="essayScore.score" class="score_num">{{ essayScore.score }}分</span></span>
          <span><span v-if="choiceScore.score + fillScore.score + simpleScore.score + essayScore.score" class="score_num">共{{ choiceScore.score+fillScore.score+simpleScore.score+essayScore.score }}分</span></span>
        </div>
        <!--      <div class="header-time">-->
        <!--        耗时：<span class='score_num'>00:00:00</span>-->
        <!--      </div>-->
      </div>
      <div ref='scrollContainer' class="content-view">
        <div class="summary-view" v-if="testPaperType === 1">
          <div class="summary-header">
          <span class='mr20'>
            <span class='mr5'>答题总数：</span>
            <span class='blue-color '>{{`${userTestpaper?userTestpaper.totalQuantity:0}道题`}}</span>
          </span>
            <span class='mr20'>
            <span class='mr5'>答对：</span>
            <span class='blue-color '>{{`${userTestpaper?userTestpaper.rightQuantity:0}道题`}}</span>
          </span>
            <span class='mr20'>
            <span class='mr5'>答错：</span>
            <span class='blue-color '>{{`${userTestpaper?userTestpaper.wrongQuantity:0}道题`}}</span>
          </span>
            <span class='mr20'>
            <span class='mr5'>正确率：</span>
            <span class='blue-color '>{{ rate(userTestpaper) }}</span>
          </span>
            <span class='summary-score'>
            <span v-if="showScore">{{ userTestpaper ? userTestpaper.score : 0 }}分</span>
            <span v-else>--分</span>
          </span>
          </div>
          <div class="summary-content">
            <div class="content-tip">
              AI综合评价
            </div>
            <div v-if='userTestpaper && userTestpaper.analysis' v-html="userTestpaper.analysis"></div>
            <div v-else>暂无评价</div>
          </div>
        </div>
        <el-collapse v-model='activeNames'>
          <el-collapse-item v-if='choiceList.length > 0' name='choice'>
            <template slot="title">
              <div class="collapse-title-view">
                <div class="title">
                  选择题
                </div>
                <div class="subtitle">
                  共{{ choiceList.length }}题
                </div>
                <div class="subtitle" v-if="choiceScore.score">
                  {{  choiceScore.score + '分' }}
                </div>
              </div>
            </template>
            <div style='width: 100%;' v-for="(item, index) in choiceList" :key="index">
              <DoExerciseItem
                :question-data="item"
                :index-string="`${index + 1}.`"
                :test-id="testId"
                :test-paper-type="testPaperType"
                @updateAnswerData="updateAnswerData($event, index, 'choice')"
              />
            </div>
          </el-collapse-item>
          <el-collapse-item v-if='fillList.length > 0' name='fill'>
            <template slot="title">
              <div class="collapse-title-view">
                <div class="title">
                  填空题
                </div>
                <div class="subtitle">
                  共{{ fillList.length }}题
                </div>
                <div class="subtitle" v-if="fillScore.score">
                  {{  fillScore.score + '分' }}
                </div>
              </div>
            </template>
            <div style='width: 100%;' v-for="(item, index) in fillList" :key="index">
              <DoExerciseItem
                :question-data="item"
                :index-string="`${index + 1}.`"
                :test-id="testId"
                :test-paper-type="testPaperType"
                @updateAnswerData="updateAnswerData($event, index, 'fill')"
              />
            </div>
          </el-collapse-item>
          <el-collapse-item v-if='simpleList.length > 0' name='simple'>
            <template slot="title">
              <div class="collapse-title-view">
                <div class="title">
                  判断题
                </div>
                <div class="subtitle">
                  共{{ simpleList.length }}题
                </div>
                <div class="subtitle" v-if="simpleScore.score">
                  {{  simpleScore.score + '分' }}
                </div>
              </div>
            </template>
            <div style='width: 100%;' v-for="(item, index) in simpleList" :key="index">
              <DoExerciseItem
                :question-data="item"
                :index-string="`${index + 1}.`"
                :test-id="testId"
                :test-paper-type="testPaperType"
                @updateAnswerData="updateAnswerData($event, index, 'simple')"
              />
            </div>
          </el-collapse-item>
          <el-collapse-item v-if='essayList.length > 0' name='essay'>
            <template slot="title">
              <div class="collapse-title-view">
                <div class="title">
                  简答题
                </div>
                <div class="subtitle">
                  共{{ essayList.length }}题
                </div>
                <div class="subtitle" v-if="essayScore.score">
                  {{  essayScore.score + '分' }}
                </div>
              </div>
            </template>
            <div style='width: 100%;' v-for="(item, index) in essayList" :key="index">
              <DoExerciseItem
                :question-data="item"
                :index-string="`${index + 1}.`"
                :test-id="testId"
                :test-paper-type="testPaperType"
                @updateAnswerData="updateAnswerData($event, index, 'essay')"
              />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <div class="bottom-view">
      <el-button v-if='testPaperType === 0' type='primary' size='mini' @click="handleSubmit" class='bottom-btn'>
        <i class='el-icon-s-promotion mr5'></i>
        <span>提交答题</span>
      </el-button>
      <el-button v-else type='primary' @click="handleSubmit" size='mini' class='bottom-btn'>
        <i class='el-icon-refresh-right mr5'></i>
        <span>重做</span>
      </el-button>
    </div>
    <NotifyView :loading="loading" ellipsis-text="AI分析中请稍等"/>
  </div>
</template>

<script>
import NotifyView from '@/components/classPro/GenerateByAIGC/notifyView'
import DoExerciseItem from '@/views/digitalbooks/read/doExercise/doExerciseItem'
import { getTestPaperQuestionList, redoTestPaper, submiteTestpaper } from '@/api/test-api'
import router from '@/router'

export default {
  name: 'DoExercise',
  components: {
    DoExerciseItem,
    NotifyView
  },
  data() {
    return {
      show: false,
      testId: '',
      questionIds: '',
      studentCourseId: router.currentRoute.query.studentCourseId || '',
      testPaperType: 0,
      userTestpaper: null,
      testTitle: '',
      testType: 0,
      content: '',
      questionList: [],
      choiceList: [],
      fillList: [],
      simpleList: [],
      essayList: [],
      showScore: false,
      analysis: '',
      loading: false,
      activeNames: ['choice', 'fill', 'simple', 'essay']
    }
  },
  computed: {
    choiceScore() {
      let score = 0
      this.choiceList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.choiceList.length }
    },
    fillScore() {
      let score = 0
      this.fillList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.fillList.length }
    },
    simpleScore() {
      let score = 0
      this.simpleList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.simpleList.length }
    },
    essayScore() {
      let score = 0
      this.essayList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.essayList.length }
    }
  },
  methods: {
    open(testId, questionIds) {
      this.show = true // 打开组件
      this.testId = testId
      this.questionIds = questionIds
      this.getExerciseData()
    },
    close() {
      this.testPaperType = 0
      this.show = false // 关闭组件
    },
    rate(data) {
      if (data) {
        const total = data.totalQuantity || 0
        const right = data.rightQuantity || 0
        if (total === 0) return '0%'
        return `${((right / total) * 100).toFixed(2)}%`
      } else {
        return '0%'
      }
    },
    async getExerciseData() {
      try {
        const { data } = await getTestPaperQuestionList({ testPaperId: Number(this.testId), questionIds: this.ids, studentCourseId: this.studentCourseId })
        this.userTestpaper = data.userTestpaper
        if (this.userTestpaper && this.userTestpaper.progressStatus === 'FINISHED') {
          this.testPaperType = 1
        }
        this.showScore = data.configScore
        this.questionList = data.questionList
        this.testTitle = data.title
        this.choiceList = data.questionList.filter((item) => { return item.questionType === 'CHOICE' })
        this.fillList = data.questionList.filter((item) => { return item.questionType === 'FILL_IN_THE_BLANK_INPUT' })
        this.simpleList = data.questionList.filter((item) => { return item.questionType === 'SIMPLE_CHOOSE' })
        this.essayList = data.questionList.filter((item) => { return item.questionType === 'ESSAY_QUESTION' })
        // this.essayList.forEach(item => {
        //   if (item.answerUser && item.answerUser.answerIds) {
        //     item.answer = item.answerUser.answerIds
        //   }
        // })
      } catch (e) {
        this.$message.error('获取试题数据失败')
      }
    },
    updateAnswerData(data, index, type) {
      if (type === 'choice') {
        this.choiceList[index].answerUser = data
      }
      if (type === 'fill') {
        this.fillList[index].answerUser = data
      }
      if (type === 'simple') {
        this.simpleList[index].answerUser = data
      }
      if (type === 'essay') {
        this.essayList[index].answerUser = data
      }
    },
    handleSubmit(refresh = true) {
      if (this.testPaperType === 0) {
        const arr = this.questionList.filter(item => { return !item.answerUser })
        if (arr.length !== 0) {
          if (refresh) {
            this.handleSubmit(false)
            return
          } else {
            this.$message.warning('请完成所有题目')
            return
          }
        }
        this.loading = true
        submiteTestpaper({ testPaperId: this.testId, questionIds: this.questionIds, studentCourseId: this.studentCourseId }, {
          authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
        }).then(res => {
          if (res && res.data) {
            this.userTestpaper = res.data.userTestpaper
          }
          this.testPaperType = 1
          this.getExerciseData()
          this.scrollToTop()
        }).finally(() => {
          this.loading = false
        })
      } else {
        redoTestPaper({ testPaperId: this.testId, studentCourseId: this.studentCourseId, questionIds: this.questionIds }, {
          authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
        }).then(res => {
          this.testPaperType = 0
          this.getExerciseData()
        }).catch(() => {
          this.$message.error('重做失败')
        })
      }
    },
    scrollToTop() {
      this.$nextTick(() => {
        const container = this.$refs.scrollContainer
        if (container) {
          // 设置滚动条位置到最顶部
          container.scrollTop = 0
        }
      })
    }
  }
}
</script>

<style scoped lang='scss'>
.do-exercise-main{
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  .close-view{
    position: absolute;
    left: 10px;
    top: 15px;
    font-size: 14px;
    display: flex;
    align-items: center;
    z-index: 101;
    i{
      font-size: 16px;
      margin-right: 5px;
    }
  }
  .main-view{
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow-y: auto;
    .header-view{
      width: 100%;
      height: 150px;
      background: linear-gradient(180deg, #A1C4FD 0%, rgba(194, 233, 251, 0) 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 30px;
      position: relative;
      .header-title{
        font-size: 26px;
        display: flex;
        align-items: center;
        color: rgba(47, 128, 237, 1);
        font-weight: 600;
      }
      .header-info{
        margin-top: 20px;
        background: white;
        border: 1px solid rgba(47, 128, 237, 0.29);
        display: flex;
        align-items: center;
        height: 30px;
        padding: 0 15px;
        font-size: 12px;
        border-radius: 3px;
      }
      .header-time{
        margin-top: 10px;
        font-size: 10px;
        background: rgba(47, 128, 237, 0.08);
        border-radius: 3px;
        display: flex;
        align-items: center;
        height: 20px;
        padding: 0 15px;
      }
    }
    .content-view{
      width: 100%;
      //height: calc(100% - 150px);
      background: white;
      //overflow-y: auto;
      padding: 0 20% 40px 20%;
      box-sizing: border-box;
      ::v-deep .el-collapse{
        border-top: 0;
      }
      ::v-deep .el-collapse-item__header{
        height: 50px;
        border-bottom: 1px solid #e6ebf5 !important;
      }
      ::v-deep .el-collapse-item__content{
        border-bottom: 0 !important;
      }
      ::v-deep .el-collapse-item__wrap{
        border-bottom: 0 !important;
      }
      .summary-view{
        width: 100%;
        min-height: 80px;
        border: 1px solid rgba(47, 128, 237, 0.44);
        background: rgba(47, 128, 237, 0.07);
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 30px;
        .summary-header {
          width: 100%;
          height: 40px;
          background: linear-gradient(90deg, #A1C4FD 0%, #C2E9FB 100%);
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 16px;
          padding: 0 20px 0 35px;
          font-weight: 500;
          .summary-score {
            color: rgba(47, 128, 237, 1);
            font-size: 22px;
          }
        }
        .summary-content{
          width: 100%;
          padding: 10px;
          box-sizing: border-box;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.87);
          line-height: 20px;
          white-space: pre-wrap;
          word-break: break-all;
          .content-tip{
            font-weight: 500;
            margin-bottom: 10px;
            background: linear-gradient(90deg, #A1C4FD 0%, #C2E9FB 100%);
            border-radius: 5px;
            height: 25px;
            line-height: 25px;
            text-align: center;
            width: 90px;
          }
        }
      }
      .collapse-title-view{
        height: 50px;
        line-height: 50px;
        width: 100%;
        display: flex;
        align-items: center;
        .title{
          height: 100%;
          font-size: 18px;
          font-weight: 700;
          border-bottom: 3px solid rgba(47, 128, 237, 1);
          display: flex;
          align-items: center;
        }
        .subtitle{
          font-size: 12px;
          background-color: rgba(230, 240, 255, 1);
          border-radius: 5px;
          color: rgba(47, 128, 237, 1);
          margin-left: 10px;
          height: 20px;
          line-height: 20px;
          padding: 1px 8px;
        }
      }
    }
  }
  .bottom-view{
    width: 100%;
    height: 60px;
    background: transparent;
    //border-top: 1px solid rgba(189, 189, 189, 1);
    //box-shadow: 0px -5px 4px 0px rgba(0, 0, 0, 0.09);
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: 0;
    .bottom-btn{
      width: 180px;
    }
  }
  .score_num{
    color:#409EFF;
    margin-left: 5px;
  }
  .blue-color{
    color: rgba(47, 128, 237, 1)
  }
  .mr5{
    margin-right: 5px;
  }
  .mr20{
    margin-right: 20px;
  }
}

</style>
