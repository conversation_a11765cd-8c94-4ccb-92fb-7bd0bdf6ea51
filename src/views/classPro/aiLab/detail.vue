<template>
  <div class="course-detail">
    <div class="header">
      <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="backToHome" />
      <div class="back" @click="backToHome">返回</div>
    </div>
    <div class="content-container">
      <div v-for="(item, index) in cardList" :key="index" class="card" @click="toAiTraining(item)">
        <div class="card-content">
          <div v-if="!item.isOnline" class="card-status">线下实践</div>
          <div v-else class="card-status">AI实验</div>
          <img v-if="item.cover" :src="item.cover" alt="" class="card-image" />
          <img v-else src="@/assets/images/default-cover.jpg" alt="" class="card-image" />
          <div class="card-title title-150width" v-html="item.trainingName"></div>
          <div class="card-desc" v-html="item.subtitle"></div>
        </div>
      </div>
    </div>
    <doAiTraing ref="doAiTraing" :student-course-id="studentCourseId" @close="getTrainingByLinkType" />
    <Scratch ref="scratchRef" :student-course-id="studentCourseId" @close="getTrainingByLinkType"/>
    <Game ref="gameRef" :student-course-id="studentCourseId" />
    <PythonView ref="pythonRef" :student-course-id="studentCourseId" @close="getTrainingByLinkType"/>
    <doExcelTraing ref="doExcelTraingRef" :student-course-id="studentCourseId" @close="getTrainingByLinkType"/>
  </div>
</template>

<script>
import PythonView from '@/components/classPro/Game/pythonView'
import Game from '@/components/classPro/Game'
import Scratch from '@/components/classPro/Scratch/index.vue'
import doAiTraing from '@/views/digitalbooks/editor/components/doAiTraing.vue'
import doExcelTraing from '@/views/digitalbooks/editor/components/doExcelTraing.vue'
import { getTrainingByLinkType, getTrainingPresetFile } from '@/api/course-api'
import { mapGetters } from 'vuex'
export default {
  components: {
    doAiTraing,
    Scratch,
    Game,
    PythonView,
    doExcelTraing
  },
  data () {
    return {
      cardList: [],
      studentCourseId: 0,
      gameList: [
        'TRAFFIC_VIOLATION_DETECTION_PRACTICE',
        'FACE_RECOGNITION_PRACTICE',
        'OBJECT_DETECTION_PRACTICE',
        'IMAGE_RECOGNITION_PRACTICE',
        'LICENSE_PLATE_RECOGNITION_PRACTICE',
        'GESTURE_SNAKE_PRACTICE',
        'GESTURE_RPS_PRACTICE',
        'IMAGE_SEGMENTATION_PRACTICE',
        'IMAGE_DIFF_PRACTICE'
      ]
    }
  },
  mounted () {
    this.studentCourseId = Number(this.$route.params.studentCourseId)
    this.getTrainingByLinkType()
  },
  updated() {
  },
  methods: {
    backToHome() {
      if (this.$route.query.from === 'lab') {
        this.$router.push('/classpro/aitraining')
      } else {
        this.$router.push('/classpro/myAitraining')
      }
    },
    async getTrainingByLinkType() {
      const { data } = await getTrainingByLinkType({
        trainingLinkType: 'AI_COURSE',
        linkSourceId: this.$route.params.id,
        studentCourseId: this.studentCourseId
      })
      this.cardList = data
    },
    toAiTraining(item) {
      if (item.trainingType === 'SCRATCH_PRACTICE') {
        this.$refs.scratchRef.open(item.trainingId)
      } else if (item.trainingType === 'COMM_PRACTICE') {
        this.$refs.doAiTraing.open(item.trainingId)
      } else if (item.trainingType === 'PYTHON_PRACTICE') {
        this.getTrainingPresetFile(item)
        // this.$refs.pythonRef.open(item.trainingId)
      } else if (item.trainingType === 'FINACE_PRACTICE') {
        this.$refs.doExcelTraingRef.open(item.trainingId)
      } else {
        this.$refs.gameRef.open(item)
      }
    },
    async getTrainingPresetFile (item) {
      const params = {
        trainingId: item.trainingId,
        userId: this.id ? this.id : null
      }
      await getTrainingPresetFile(params)
        .then(response => {
          if (response.code === 200) {
            window.open(`https://binguoketang.com/jupyterhub/hub/logout`, '_blank')
            // this.$refs.pythonRef.open(item.trainingId)
          } else {
            this.$message.error(response.message || '获取文件失败')
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error('获取文件失败')
        })
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  }
}

</script>

  <style lang='scss' scoped>
  .course-detail {
    height: 100%;
    padding: 10px;
    width: 100%;
    overflow-y: auto;
    @include scrollBar;

    .header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      img {
        width: 13px;
        height: 11px;
        cursor: pointer;
      }

      .back {
        font-size: var(--font-size-L);
        color: #1C1B1A;
        margin: 0 20px 0 8px;
        cursor: pointer;
      }
    }

    .content-container {
      width: 100%;
      border-radius: 10px;
      padding: 20px;
      overflow-y: auto;
      @include scrollBar;
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      background: #fff;
      > * {
        flex: 0 0 calc(50% - 10px);
      }

      .card {
        background: #F8FAFF;
        border-radius: 8px;
        padding: 10px;
        width: 300px;
        height: 160px;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;
        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .card-image {
          width: 140px;
          height: 140px;
          object-fit: cover;
          border-radius: 10px;
        }
        .card-content {
          position: relative;
          .card-status {
            position: absolute;
            right: 0;
            top: 0;
            background: #DEF3FF;
            color: #2F80ED;
            padding: 5px 10px;
            border-radius: 5px;
          }
          .card-title {
            font-size: var(--font-size-L);
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            @include ellipses(2);
            position: absolute;
            left: 160px;
            top: 5px;
          }
          .title-150width{
            width: 150px;
          }
          .title-230width{
            width: 230px;
          }
          .card-desc {
            font-size: var(--font-size-M);
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
            position: absolute;
            left: 160px;
            width: 200px;
            top: 60px;
            @include ellipses(4);
          }

          .card-status {
            font-size: 12px;
            color: #3479FF;
          }
        }
      }
    }

    .container {
      width: 100%;
      padding: 20px;
      background: #FFFFFF;
      box-shadow: 0 3px 14px rgba(233,240,255,0.5);
      border-radius: 10px;
      margin-bottom: 10px;
    }

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .line {
        width: 3px;
        height: 16px;
        background: #3479FF;
        border-radius: 3.5px;
        margin-right: 8px;
      }

      span {
        font-weight: 500;
        font-size: var(--font-size-L);
        color: #1C1B1A;
      }
    }

    .video-content,
    .training-content {
      position: relative;
    }

    .video-list,
    .training-list {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 15px;
      transition: max-height 0.3s ease-in-out;

      &.collapsed {
        max-height: 190px;
        overflow: hidden;
      }

      &:not(.collapsed) {
        max-height: 2000px;
      }
    }

    .video-item,
    .training-item {
      height: 180px;
      cursor: pointer;

      .video-cover,
      .training-cover {
        height: 150px;
        position: relative;
        width: 100%;
        border-radius: 8px;
        overflow: hidden;

        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 40px;
          height: 40px;
          background: rgba(0,0,0,0.5);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
        }
      }

      .video-title,
      .training-title {
        height: 30px;
        margin-top: 5px;
        font-size: var(--font-size-M);
        color: #1C1B1A;
        @include ellipses(1);
      }
    }

    .expand-btn {
      position: absolute;
      bottom: -15px;
      right: 0;
      cursor: pointer;
      display: flex;
      align-items: center;
      color: #999;
      padding: 5px 10px;
      z-index: 1;
      background: #fff;

      i {
        margin-left: 5px;
        transition: transform 0.3s;

        &.is-expanded {
          transform: rotate(180deg);
        }
      }
    }

    /* 添加视频模态框样式 */
    .video-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.85);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;

      .video-container {
        width: 80%;
        max-width: 1200px;
        background: #000;
        border-radius: 8px;
        overflow: hidden;

        .video-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px 20px;
          background: rgba(0, 0, 0, 0.8);

          .video-title {
            color: #fff;
            font-size: var(--font-size-L);
          }

          .el-icon-close {
            color: #fff;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            transition: all 0.3s;

            &:hover {
              transform: scale(1.1);
              color: #f56c6c;
            }
          }
        }

        .video-player {
          width: 100%;
          aspect-ratio: 16/9;
          background: #000;
          outline: none;
        }
    }
      }
    }
</style>
