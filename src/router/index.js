import Vue from 'vue'
import Router from 'vue-router'

import Home from '@/layout/classPro/index.vue'
import Dashboard from '@/views/classPro/dashboard/index-new2.vue'

Vue.use(Router)

export const constantRoutes = [
  {
    path: '/',
    redirect: '/classpro'
  },
  {
    path: '/loginweb',
    component: () => import('@/views/classPro/login/index.vue'),
    name: 'login2',
    meta: {
      title: '登录'
    }
  },
  {
    path: '/jhub-login',
    component: () => import('@/views/classPro/login/oauthLogin.vue'),
    name: 'jhubLogin',
    meta: {
      title: '登录'
    }
  },
  {
    path: '/classpro/login',
    component: () => import('@/views/classPro/login/index.vue'),
    name: 'login',
    meta: {
      title: '登录'
    }
  },
  {
    path: '/classpro/register',
    component: () => import('@/views/classPro/register/index.vue'),
    name: 'register',
    meta: {
      title: '注册'
    }
  },
  {
    path: '/help',
    component: () => import('@/views/classPro/help/index.vue'),
    name: 'help',
    meta: {
      title: '帮助'
    }
  },
  {
    path: '/h5/',
    redirect: '/h5/login',
    component: () => import('@/layout/H5/index.vue'),
    children: [
      {
        path: 'ScreenCasting',
        component: () => import('@/views/H5/ScreenCasting.vue'),
        name: 'ALogin',
        meta: {
          title: '登录',
          type: 'H5'
        }
      },
      {
        path: 'login',
        component: () => import('@/views/parent/login.vue'),
        name: 'ALogin',
        meta: {
          title: '登录',
          type: 'H5'
        }
      },
      {
        path: 'checkLogin',
        component: () => import('@/views/parent/checkLogin.vue'),
        name: 'AcheckLogin',
        meta: {
          title: '登录中',
          type: 'H5'
        }
      },
      {
        path: 'checkPraise',
        component: () => import('@/views/parent/checkPraise.vue'),
        name: 'AcheckPraise',
        meta: {
          title: '登录中',
          type: 'H5'
        }
      },
      {
        path: 'hassClassInfo',
        component: () => import('@/views/parent/bind/hassClassInfo.vue'),
        name: 'AHassClassInfo',
        meta: {
          title: '绑定学生',
          type: 'H5'
        }
      },
      {
        path: 'info',
        component: () => import('@/views/parent/bind/info.vue'),
        name: 'Ainfo',
        meta: {
          title: '信息',
          type: 'H5'
        }
      },
      {
        path: 'index',
        component: () => import('@/views/H5/index.vue'),
        name: 'Aindex',
        meta: {
          title: '缤果活动',
          type: 'H5'
        }
      },
      {
        path: 'activity',
        component: () => import('@/views/H5/activity/index.vue'),
        name: 'Activity',
        meta: {
          title: '缤果活动',
          type: 'H5'
        }
      },
      {
        path: 'list',
        component: () => import('@/views/H5/activity/list.vue'),
        name: 'ActivityList',
        meta: {
          title: '缤果活动列表',
          type: 'H5'
        }
      },
      {
        path: 'my',
        component: () => import('@/views/H5/my.vue'),
        name: 'My',
        meta: {
          title: '个人',
          type: 'H5'
        }
      },
      {
        path: 'my-info',
        component: () => import('@/views/H5/myInfo.vue'),
        name: 'MyInfo',
        meta: {
          title: '我的信息',
          type: 'H5'
        }
      },
      {
        path: 'my-jion',
        component: () => import('@/views/H5/myJion.vue'),
        name: 'MyJion',
        meta: {
          title: '我的参赛',
          type: 'H5'
        }
      },
      {
        path: 'attachment',
        component: () => import('@/views/H5/activity/attachment.vue'),
        name: 'Attachment',
        meta: {
          title: '缤果活动',
          type: 'H5'
        }
      },
      {
        path: 'apply',
        component: () => import('@/views/H5/apply.vue'),
        name: 'Apply',
        meta: {
          title: '活动报名',
          type: 'H5'
        }
      },
      {
        path: 'submit-work',
        component: () => import('@/views/H5/submitWork.vue'),
        name: 'SubmitWorkpply',
        meta: {
          title: '活动报名',
          type: 'H5'
        }
      },
      {
        path: 'check-work',
        component: () => import('@/views/H5/checkWork.vue'),
        name: 'CheckWorkpply',
        meta: {
          title: '作品详情',
          type: 'H5'
        }
      },
      {
        path: 'submit',
        component: () => import('@/views/H5/submit.vue'),
        name: 'Submit',
        meta: {
          title: '作品页',
          type: 'H5'
        }
      },
      {
        path: 'check',
        component: () => import('@/views/H5/check.vue'),
        name: 'Check',
        meta: {
          title: '作品查看',
          type: 'H5'
        }
      }
    ]
  },
  {
    path: '/activity/expert',
    component: () => import('@/layout/classPro/activity.vue'),
    children: [
      {
        path: ':activityId',
        component: () => import('@/views/classPro/activity/detail'),
        name: 'activityExpert',
        meta: {
          title: '专家活动详情'
        }
      },
      {
        path: 'review/:activityId/:expertToken',
        component: () => import('@/views/classPro/activity/review'),
        name: 'activityExpertReview',
        meta: {
          title: '活动专家评审'
        }
      }
    ]
  },
  {
    path: '/activity/work/:activityId/:workId',
    component: () => import('@/views/classPro/activity/work/index.vue'),
    name: 'workDetail',
    meta: {
      title: '作品详情'
    }
  },
  {
    path: '/edit/course',
    component: () => import('@/views/classPro/educational/components/gradeIfram.vue'),
    name: 'educationalIframe',
    meta: {
      title: '教务'
    }
  },
  {
    path: '/parent',
    redirect: '/parent/login',
    component: () => import('@/layout/parent/index.vue'),
    children: [
      {
        path: 'login',
        component: () => import('@/views/parent/login.vue'),
        name: 'Plogin',
        meta: {
          title: '家长端'
        }
      },
      {
        path: 'checkLogin',
        component: () => import('@/views/parent/checkLogin.vue'),
        name: 'PcheckLogin',
        meta: {
          title: '家长端'
        }
      },
      {
        path: 'hassClassInfo',
        component: () => import('@/views/parent/bind/hassClassInfo.vue'),
        name: 'PHassClassInfo',
        meta: {
          title: '家长端'
        }
      },
      {
        path: 'course',
        component: () => import('@/layout/parent/course.vue'),
        children: [
          {
            path: 'homework/:aicourseId/:studentCourseId/:unitId/:sectionId/:classId',
            component: () => import('@/views/parent/course/homework.vue'),
            name: 'PHomework',
            meta: {
              title: '作业'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/chuanEdu',
    component: () => import('@/views/parent/chuanEdu.vue'),
    name: 'chuanEdu',
    meta: {
      title: '缤果课堂'
    }
  },
  {
    path: '/chuanEdu/detail',
    component: () => import('@/views/parent/chuanEduDetail.vue'),
    name: 'chuanDetail',
    meta: {
      title: '缤果课堂'
    }
  },
  {
    path: '/datacenter',
    redirect: '/datacenter/login',
    component: () => import('@/layout/datacenter/index.vue'),
    children: [
      {
        path: 'login',
        component: () => import('@/views/datacenter/login.vue'),
        name: 'DLogin',
        meta: {
          title: '登录'
        }
      },
      {
        path: 'index',
        component: () => import('@/views/datacenter/index.vue'),
        name: 'Dindex',
        meta: {
          title: '数据统计平台'
        }
      },
      {
        path: 'course',
        component: () => import('@/views/datacenter/course.vue'),
        name: 'Dcourse',
        meta: {
          title: '数据统计平台'
        }
      }
    ]
  },
  {
    path: '/publisher',
    redirect: '/publisher/login',
    component: () => import('@/layout/publishingReview/index.vue'),
    children: [
      {
        path: 'login/:publisheId',
        component: () => import('@/views/publishingReview/login/login.vue'),
        name: 'PLogin',
        meta: {
          title: '数字教材出版平台'
        }
      },
      {
        path: 'home',
        component: () => import('@/views/publishingReview/index/index.vue'),
        name: 'Phome',
        meta: {
          title: '数字教材出版平台'
        }
      },
      {
        path: 'review',
        component: () => import('@/views/publishingReview/review/index.vue'),
        name: 'review',
        meta: {
          title: '数字教材出版平台'
        }
      }
    ]
  },
  {
    path: '/expert',
    redirect: '/expert/login',
    component: () => import('@/layout/publishingReview/index.vue'),
    children: [
      {
        path: 'login',
        component: () => import('@/views/publishingReview/login/login.vue'),
        name: 'ELogin',
        meta: {
          title: '数字教材出版平台'
        }
      },
      {
        path: 'home',
        component: () => import('@/views/publishingReview/index/index.vue'),
        name: 'EPhome',
        meta: {
          title: '数字教材出版平台'
        }
      },
      {
        path: 'review',
        component: () => import('@/views/publishingReview/review/index.vue'),
        name: 'Ereview',
        meta: {
          title: '数字教材出版平台'
        }
      }
    ]
  },
  {
    path: '/community',
    component: () => import('@/views/community/index.vue'),
    name: 'community',
    meta: {
      title: '社区'
    }
  },
  {
    path: '/author',
    redirect: '/author/login',
    component: () => import('@/layout/publishingReview/index.vue'),
    children: [
      {
        path: 'login',
        component: () => import('@/views/publishingReview/login/login.vue'),
        name: 'AuthorLogin',
        meta: {
          title: '缤果图书智能创作平台'
        }
      },
      {
        path: 'home',
        component: () => import('@/views/publishingReview/author/home.vue'),
        name: 'AuthorHome',
        meta: {
          title: '缤果图书智能创作平台'
        }
      },
      {
        path: 'task',
        component: () => import('@/views/publishingReview/author/task/index.vue'),
        name: 'AuthorTasK',
        meta: {
          title: '任务工单'
        }
      },
      {
        path: 'class',
        component: () => import('@/views/publishingReview/author/class/index.vue'),
        name: 'AuthorClass',
        meta: {
          title: '课时管理'
        }
      },
      {
        path: 'resources',
        component: () => import('@/views/digitalbooks/resource/index.vue'),
        name: 'Resources',
        meta: {
          title: '资源管理'
        }
      },
      {
        path: 'lectureNotes',
        component: () => import('@/views/publishingReview/author/lectureNotes/index.vue'),
        name: 'AuthorLectureNotes',
        meta: {
          title: '智能讲义'
        }
      },
      {
        path: 'lectureNotesEditor',
        component: () => import('@/views/publishingReview/author/lectureNotes/editor.vue'),
        name: 'lectureNotesEditor',
        meta: {
          title: '智能讲义编辑器'
        }
      },
      {
        path: 'lectureNotesPreview',
        component: () => import('@/views/publishingReview/author/lectureNotes/read.vue'),
        name: 'lectureNotesPreview',
        meta: {
          title: '智能讲义预览'
        }
      }
    ]
  },
  {
    path: '/lectureNotesRead',
    component: () => import('@/views/publishingReview/author/lectureNotes/read.vue'),
    name: 'lectureNotesRead',
    meta: {
      title: '智能讲义阅读'
    }
  },
  {
    path: '/editor',
    component: () => import('@/views/digitalbooks/editor/index.vue'),
    name: 'editor',
    meta: {
      title: '编辑器'
    }
  },
  {
    path: '/authorInvite',
    component: () => import('@/views/digitalbooks/author/index.vue'),
    name: 'authorInvite',
    meta: {
      title: '作者邀请'
    }
  },
  {
    path: '/digitalbooks',
    // redirect: '/digitalbooks/read',
    component: () => import('@/layout/digitalbooks/index.vue'),
    children: [
      {
        path: 'read',
        component: () => import('@/views/digitalbooks/read/index.vue'),
        name: 'digRead',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'authreview',
        component: () => import('@/views/digitalbooks/read/authreview.vue'),
        name: 'digCodeRead',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'jupyterhub',
        component: () => import('@/views/digitalbooks/read/jupyterhub.vue'),
        name: 'digTest'
      },
      {
        path: 'lectureNotes',
        component: () => import('@/views/publishingReview/author/lectureNotes/index.vue'),
        name: 'digLectureNotes',
        meta: {
          title: '智能讲义'
        }
      },
      {
        path: 'resource',
        component: () => import('@/views/digitalbooks/resource/index.vue'),
        name: 'digResource',
        meta: {
          title: '资源库'
        }
      },
      {
        path: 'attendClass',
        component: () => import('@/views/digitalbooks/attendClass/index.vue'),
        name: 'digAttendClass',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'inClass',
        component: () => import('@/views/digitalbooks/attendClass/inClass.vue'),
        name: 'digInClass',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'backPack',
        component: () => import('@/views/digitalbooks/attendClass/backPack.vue'),
        name: 'digBackPack',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'task',
        component: () => import('@/views/digitalbooks/task/home/<USER>'),
        name: 'digTaskMode',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'task-t',
        component: () => import('@/views/digitalbooks/task/teacher/index.vue'),
        name: 'digTaskT',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'push-task',
        component: () => import('@/views/digitalbooks/task/teacher/push.vue'),
        name: 'digPushTask',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'task-s',
        component: () => import('@/views/digitalbooks/task/student/index.vue'),
        name: 'digTaskS',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'gradeStatistics',
        component: () => import('@/views/digitalbooks/gradeStatistics/index.vue'),
        name: 'digGradeStatistics',
        meta: {
          title: '成绩统计'
        }
      }
    ]
  },
  {
    path: '/bingoBook',
    component: () => import('@/layout/bingoBook/index.vue'),
    redirect: '/bingoBook/home',
    name: 'bingoBook',
    meta: {
      title: 'BingoBook'
    },
    children: [
      {
        path: 'login',
        component: () => import('@/views/bingoBook/login.vue'),
        meta: {
          title: 'BingoBook'
        }
      },
      {
        path: 'home',
        component: () => import('@/views/bingoBook/index.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: true
        }
      },
      {
        path: 'help',
        component: () => import('@/views/bingoBook/help.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: true
        }
      },
      {
        path: 'myClasslist',
        component: () => import('@/views/bingoBook/myClass.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: true
        }
      },
      {
        path: 'myInfoList',
        component: () => import('@/views/bingoBook/infoList.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: true
        }
      },
      {
        path: 'bookInfo',
        component: () => import('@/views/bingoBook/bookInfo.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: true
        }
      },
      {
        path: 'bookRead',
        component: () => import('@/views/bingoBook/bookRead.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: false
        }
      },
      {
        path: 'officeView',
        component: () => import('@/views/bingoBook/officeView.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: false
        }
      },
      {
        path: 'test',
        component: () => import('@/views/bingoBook/test.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: false
        }
      },
      {
        path: 'task',
        component: () => import('@/views/bingoBook/task.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: false
        }
      },
      {
        path: 'taskDetail',
        component: () => import('@/views/bingoBook/taskDetail.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: false
        }
      },
      {
        path: 'group',
        component: () => import('@/views/bingoBook/group.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: false
        }
      },
      {
        path: 'progress',
        component: () => import('@/views/bingoBook/progress.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: false
        }
      },
      {
        path: 'note',
        component: () => import('@/views/bingoBook/note.vue'),
        meta: {
          title: 'BingoBook',
          keepAlive: true
        }
      }
    ]
  },
  {
    path: '/readFromBack',
    component: () => import('@/views/digitalbooks/read/component/readComponent.vue'),
    meta: {
      title: '阅读',
      keepAlive: false
    }
  }
]

export const asyncRoutes = [
  {
    path: '/network',
    component: () => import('@/layout/classPro/index-old.vue'),
    children: [
      {
        path: 'network-check',
        component: () => import('@/views/classPro/networkCheck/index.vue'),
        name: 'NetworkCheck',
        meta: {
          title: '设备检测'
        }
      }
    ]
  },
  // {
  //   path: '/classPro/skyClass',
  //   component: () => import('@/layout/classPro/index-old.vue'),
  //   children: [
  //     {
  //       path: 'allCourse/:id',
  //       component: () => import('@/views/classPro/skyClass/allCourse/index.vue'),
  //       name: 'allCourse',
  //       meta: {
  //         title: '全部课程'
  //       }
  //     }
  //   ]
  // },
  {
    path: '/classPro/skyClass/allCourse/:id',
    component: () => import('@/views/classPro/skyClass/allCourse/index.vue'),
    name: 'allCourse',
    meta: {
      title: '全部课程'
    }
  },
  // {
  //   path: '/classPro/skyClass/lessonInfo/:lessonId',
  //   component: () => import('@/views/classPro/skyClass/lessonInfo/index.vue'),
  //   name: 'lessonInfo',
  //   meta: {
  //     title: '单节课程'
  //   }
  // },
  {
    path: '/classpro',
    // component: () => import('@/layout/classPro/index.vue'),
    component: Home,
    children: [
      {
        path: '/',
        // component: () => import('@/views/classPro/dashboard/index-new.vue'),
        component: Dashboard,
        name: 'dashboard',
        meta: {
          title: '首页'
        }
      },
      {
        path: 'calendar',
        component: () => import('@/views/classPro/dashboard/calendar/index.vue'),
        name: 'calendar',
        meta: {
          title: '课程日历'
        }
      },
      {
        path: 'special',
        component: () => import('@/views/classPro/special/index.vue'),
        name: 'special',
        meta: {
          title: '专用教室'
        }
      },
      {
        path: 'skyClass',
        component: () => import('@/views/classPro/skyClass/index.vue'),
        name: 'skyClass',
        meta: {
          title: '空中课堂'
        }
      },
      // {
      //   path: 'skyClass/allCourse/:id',
      //   component: () => import('@/views/classPro/skyClass/allCourse/index.vue'),
      //   name: 'allCourse',
      //   meta: {
      //     title: '全部课程'
      //   }
      // },
      {
        path: 'skyClass/lessonInfo/:lessonId',
        component: () => import('@/views/classPro/skyClass/lessonInfo/index.vue'),
        name: 'lessonInfo',
        meta: {
          title: '单节课程'
        }
      },
      {
        path: 'aiCourse',
        component: () => import('@/views/classPro/aiCourse/index.vue'),
        name: 'aiCoursePage',
        meta: {
          title: '双师AI课'
        }
      },
      {
        path: 'skyCourse',
        component: () => import('@/views/classPro/skyCourse/index.vue'),
        name: 'skyCoursePage',
        meta: {
          title: '双师AI课'
        }
      },
      {
        path: 'digitalbooks',
        component: () => import('@/views/digitalbooks/home/<USER>'),
        name: 'digitalbooksPage',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'digitalbooks/detail',
        component: () => import('@/views/digitalbooks/home/<USER>'),
        name: 'digitalbooksPageDetail',
        meta: {
          title: '数字教材'
        }
      },
      {
        path: 'activity',
        component: () => import('@/views/classPro/activity/index.vue'),
        name: 'activityPage',
        meta: {
          title: '双师AI课'
        }
      },
      {
        path: 'myCourse',
        component: () => import('@/views/classPro/myCourse/index.vue'),
        name: 'myCoursePage',
        meta: {
          title: '双师AI课'
        }
      },
      {
        path: 'myOnlineClass',
        component: () => import('@/views/classPro/myOnlineClass/index.vue'),
        name: 'myOnlineClass',
        meta: {
          title: '我的在线课堂'
        }
      },
      {
        path: 'mySubject',
        component: () => import('@/views/classPro/mySubject/index.vue'),
        name: 'mySubject',
        meta: {
          title: '学科教研'
        }
      },
      {
        path: 'mySubject/detail/:id',
        component: () => import('@/views/classPro/mySubject/detail.vue'),
        name: 'mySubjectDetail',
        meta: {
          title: '学科教研'
        }
      },
      {
        path: 'myResource',
        component: () => import('@/views/classPro/myResource/index.vue'),
        name: 'myResource',
        meta: {
          title: '数字资源'
        }
      },
      {
        path: 'myResource/detail/:id',
        component: () => import('@/views/classPro/myResource/detail.vue'),
        name: 'resourceDetail',
        meta: {
          title: '数字资源'
        }
      },
      {
        path: 'aitraining',
        component: () => import('@/views/classPro/aiLab/index.vue'),
        name: 'aitraining',
        meta: {
          title: 'AI实验室'
        }
      },
      {
        path: 'aitraining/detail/:id/:studentCourseId',
        component: () => import('@/views/classPro/aiLab/detail.vue'),
        name: 'aitrainingDetail',
        meta: {
          title: 'AI实验室'
        }
      },
      {
        path: 'aitraining/finacePractice',
        component: () => import('@/views/digitalbooks/editor/components/doExcelTraing.vue'),
        name: 'aitrainingExcel',
        meta: {
          title: 'Excel实验'
        }
      },
      {
        path: 'myAitraining',
        component: () => import('@/views/classPro/myAIExperiment/index.vue'),
        name: 'myAitraining',
        meta: {
          title: '我的AI实验'
        }
      },
      {
        path: 'myDigitalbooks',
        component: () => import('@/views/digitalbooks/myDigitalbooks/index.vue'),
        name: 'myDigitalbooksPage',
        meta: {
          title: '双师AI课'
        }
      },

      {
        path: 'myActivity',
        component: () => import('@/views/classPro/myActivity/index.vue'),
        name: 'myActivityPage',
        meta: {
          title: '双师AI课'
        }
      },
      {
        path: 'myData',
        component: () => import('@/views/classPro/myData/index.vue'),
        name: 'myDataPage',
        meta: {
          title: '双师AI课'
        }
      },
      {
        path: 'myClass',
        component: () => import('@/views/classPro/myClass/index.vue'),
        name: 'myClassPage',
        meta: {
          title: '双师AI课'
        }
      },
      {
        path: 'onlineClass',
        component: () => import('@/views/classPro/onlineClass/index.vue'),
        name: 'onlineClass',
        meta: {
          title: '在线课堂'
        }
      },
      {
        path: 'course/package/:categoryId/:packageId/:type',
        component: () => import('@/views/classPro/course/package/index.vue'),
        name: 'coursePackageDetail',
        meta: {
          title: '课程包详情'
        }
      },
      {
        path: 'course/detail/:categoryId',
        component: () => import('@/views/classPro/course/detail/index.vue'),
        name: 'courseDetail',
        meta: {
          title: '课程详情',
          keepAlive: false // 需要缓存
        }
      },
      {
        path: 'test/geolocation',
        component: () => import('@/views/test/geolocation.vue'),
        name: 'geolocationTest',
        meta: {
          title: '地理位置测试',
          keepAlive: false
        }
      }
    ]
  },
  {
    path: '/classpro/classroom/:courseId',
    component: () => import('@/views/classPro/classroom/index.vue'),
    name: 'classroom',
    meta: {
      title: '备课授课'
    }
  },
  {
    path: '/course',
    component: () => import('@/layout/classPro/index-old.vue'),
    children: [
      {
        path: '/',
        component: () => import('@/views/classPro/course/index.vue'),
        name: 'course',
        meta: {
          title: '课程中心',
          keepAlive: true // 需要缓存
        }
      },
      {
        path: 'team/:categoryId',
        component: () => import('@/views/classPro/course/team/index.vue'),
        name: 'courseTeam',
        meta: {
          title: '专家团队'
        }
      }
    ]
  },
  {
    path: '/data',
    component: () => import('@/layout/classPro/index-old.vue'),
    children: [
      {
        path: '/',
        component: () => import('@/views/classPro/data/index.vue'),
        name: 'data',
        meta: {
          title: '数据中心'
        }
      }
    ]
  },
  {
    path: '/educational',
    component: () => import('@/layout/classPro/index-old.vue'),
    children: [
      {
        path: '/',
        component: () => import('@/views/classPro/educational/index.vue'),
        name: 'educational',
        meta: {
          title: '教务'
        }
      },
      {
        path: 'classinfo',
        component: () => import('@/views/classPro/educational/classinfo.vue'),
        name: 'Eclassinfo',
        meta: {
          title: '班级详情'
        }
      },
      {
        path: 'diginfo',
        component: () => import('@/views/digitalbooks/myDigitalbooks/components/classinfo.vue'),
        name: 'Diginfo',
        meta: {
          title: '教材详情'
        }
      }
    ]
  },
  {
    path: '/activity',
    component: () => import('@/layout/classPro/index.vue'),
    children: [
      {
        path: '/',
        component: () => import('@/views/classPro/activity/index.vue'),
        name: 'activity',
        meta: {
          title: '活动中心'
        }
      },
      {
        path: 'detail/:activityId',
        component: () => import('@/views/classPro/activity/detail'),
        name: 'activitydetail',
        meta: {
          title: '活动详情',
          keepAlive: true // 需要缓存
        }
      },
      {
        path: 'news/:noticeId',
        component: () => import('@/views/classPro/activity/news/detail'),
        name: 'newsDetail',
        meta: {
          title: '新闻详情'
        }
      },
      {
        path: 'work/:activityId/:workId',
        component: () => import('@/views/classPro/activity/work/index.vue'),
        name: 'workDetail',
        meta: {
          title: '作品详情'
        }
      }
    ]
  },
  {
    path: '/parent',
    component: () => import('@/layout/parent/index.vue'),
    children: [
      {
        path: 'index',
        component: () => import('@/views/parent/index.vue'),
        name: 'Pindex',
        meta: {
          title: '家长端'
        }
      },
      {
        path: 'info',
        component: () => import('@/views/parent/bind/info.vue'),
        name: 'Pinfo',
        meta: {
          title: '家长端'
        }
      },
      {
        path: 'change',
        component: () => import('@/views/parent/bind/change.vue'),
        name: 'Pchange',
        meta: {
          title: '家长端'
        }
      },
      {
        path: 'home',
        component: () => import('@/views/parent/home.vue'),
        name: 'Phome',
        meta: {
          title: '家长端'
        }
      },
      {
        path: 'course',
        component: () => import('@/layout/parent/course.vue'),
        children: [
          {
            path: ':aicourseId/:studentCourseId',
            component: () => import('@/views/parent/course'),
            name: 'PCourse',
            meta: {
              title: '课程列表'
            }
          },
          {
            path: 'report/:aicourseId/:studentCourseId/:unitId',
            component: () => import('@/views/parent/course/report.vue'),
            name: 'PReport',
            meta: {
              title: '课程报告'
            }
          }
        ]
      },
      {
        path: 'result',
        component: () => import('@/views/parent/result.vue'),
        name: 'PResult',
        meta: {
          title: '家长端'
        }
      }
    ]
  },
  {
    path: '/doTrainingDetail/:id/:studentCourseId',
    component: () => import('@/views/digitalbooks/editor/components/doAiTraing.vue'),
    meta: {
      title: 'AI实验室'
    }
  },
  {
    path: '/doExcelTrainingDetail/finacePractice',
    component: () => import('@/views/digitalbooks/editor/components/doExcelTraing.vue'),
    meta: {
      title: 'Excel实验'
    }
  },
  {
    path: '/AIChat',
    component: () => import('@/components/classPro/AIChat/index.vue'),
    meta: {
      title: 'AI实验室'
    }
  },
  {
    path: '/pyhtonTraining',
    component: () => import('@/components/classPro/Game/pythonView.vue'),
    name: 'pyhtonTraining',
    meta: {
      title: 'python实验'
    }
  },
]

const createRouter = () => new Router({
  mode: 'hash', // require service support
  scrollBehavior: () => ({ x: 0, y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

export default router
